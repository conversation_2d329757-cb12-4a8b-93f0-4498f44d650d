#!/usr/bin/env python3
"""
Synthetic Data Generator for SisaRasa Recipe Recommendation System

This script generates realistic synthetic data to populate the MongoDB database
with diverse user interactions, reviews, search patterns, and community engagement
to demonstrate the effectiveness of the KNN and hybrid recommendation algorithms.
"""

import json
import random
import pymongo
import bcrypt
from datetime import datetime, timedelta
from bson.objectid import ObjectId
from collections import defaultdict
import numpy as np

# Configuration
MONGO_URI = 'mongodb://localhost:27017/'
DATABASE_NAME = 'sisarasa'

# User personas and characteristics
USER_PERSONAS = {
    'casual_cook': {
        'search_frequency': 'low',
        'recipe_saves': 'few',
        'review_frequency': 'low',
        'preferred_cuisines': ['International', 'American', 'Italian'],
        'dietary_restrictions': [],
        'cooking_skill': 'beginner'
    },
    'food_enthusiast': {
        'search_frequency': 'high',
        'recipe_saves': 'many',
        'review_frequency': 'high',
        'preferred_cuisines': ['Italian', 'French', 'Asian', 'Mediterranean'],
        'dietary_restrictions': [],
        'cooking_skill': 'intermediate'
    },
    'health_conscious': {
        'search_frequency': 'medium',
        'recipe_saves': 'medium',
        'review_frequency': 'medium',
        'preferred_cuisines': ['Mediterranean', 'Asian', 'International'],
        'dietary_restrictions': ['vegetarian', 'low-sodium'],
        'cooking_skill': 'intermediate'
    },
    'busy_parent': {
        'search_frequency': 'medium',
        'recipe_saves': 'medium',
        'review_frequency': 'low',
        'preferred_cuisines': ['American', 'International', 'Mexican'],
        'dietary_restrictions': [],
        'cooking_skill': 'intermediate'
    },
    'vegan_lifestyle': {
        'search_frequency': 'high',
        'recipe_saves': 'many',
        'review_frequency': 'high',
        'preferred_cuisines': ['Mediterranean', 'Asian', 'International'],
        'dietary_restrictions': ['vegan', 'gluten-free'],
        'cooking_skill': 'advanced'
    },
    'college_student': {
        'search_frequency': 'medium',
        'recipe_saves': 'few',
        'review_frequency': 'low',
        'preferred_cuisines': ['American', 'International', 'Asian'],
        'dietary_restrictions': [],
        'cooking_skill': 'beginner'
    }
}

# Realistic user data
FIRST_NAMES = [
    'Emma', 'Liam', 'Olivia', 'Noah', 'Ava', 'Ethan', 'Sophia', 'Mason',
    'Isabella', 'William', 'Mia', 'James', 'Charlotte', 'Benjamin', 'Amelia',
    'Lucas', 'Harper', 'Henry', 'Evelyn', 'Alexander', 'Abigail', 'Michael',
    'Emily', 'Daniel', 'Elizabeth', 'Matthew', 'Sofia', 'Aiden', 'Avery',
    'Jackson', 'Ella', 'Samuel', 'Madison', 'David', 'Scarlett', 'Joseph',
    'Victoria', 'Logan', 'Aria', 'Andrew', 'Grace', 'Joshua', 'Chloe',
    'Christopher', 'Camila', 'Gabriel', 'Penelope', 'Ryan', 'Riley'
]

LAST_NAMES = [
    'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller',
    'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez',
    'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin',
    'Lee', 'Perez', 'Thompson', 'White', 'Harris', 'Sanchez', 'Clark',
    'Ramirez', 'Lewis', 'Robinson', 'Walker', 'Young', 'Allen', 'King',
    'Wright', 'Scott', 'Torres', 'Nguyen', 'Hill', 'Flores', 'Green',
    'Adams', 'Nelson', 'Baker', 'Hall', 'Rivera', 'Campbell', 'Mitchell'
]

# Common ingredient combinations for realistic searches
INGREDIENT_COMBINATIONS = [
    ['chicken', 'rice', 'vegetables'],
    ['beef', 'potatoes', 'onions'],
    ['pasta', 'tomatoes', 'cheese'],
    ['eggs', 'bread', 'milk'],
    ['salmon', 'lemon', 'herbs'],
    ['tofu', 'soy sauce', 'ginger'],
    ['beans', 'rice', 'peppers'],
    ['mushrooms', 'garlic', 'herbs'],
    ['spinach', 'cheese', 'eggs'],
    ['chicken', 'curry', 'coconut milk'],
    ['ground beef', 'onions', 'tomatoes'],
    ['shrimp', 'garlic', 'butter'],
    ['pork', 'apples', 'onions'],
    ['vegetables', 'olive oil', 'herbs'],
    ['fish', 'lemon', 'vegetables']
]

# Enhanced review templates for different ratings with more variety and realism
REVIEW_TEMPLATES = {
    5: [
        "Absolutely amazing! This recipe is now a family favorite.",
        "Perfect recipe! Easy to follow and delicious results.",
        "Outstanding! Will definitely make this again.",
        "Incredible flavors! My guests loved it.",
        "Best recipe I've tried in a long time!",
        "Fantastic! Even my picky kids enjoyed it.",
        "Wonderful recipe with clear instructions.",
        "Exceeded my expectations! Highly recommend.",
        "This is perfection! Made it three times already.",
        "Restaurant quality at home! So impressed.",
        "Five stars! My new go-to recipe.",
        "Absolutely delicious! Everyone asked for the recipe.",
        "Flawless execution and amazing taste.",
        "This recipe is a keeper! Made my day.",
        "Incredible! Better than I expected.",
        "Perfect balance of flavors. Love it!",
        "Amazing recipe! Turned out exactly as described.",
        "Phenomenal! Will be making this regularly.",
        "Excellent! Easy to follow and tastes great.",
        "Outstanding results! Highly recommend to everyone."
    ],
    4: [
        "Really good recipe! Made a few small adjustments.",
        "Great flavors, though it took longer than expected.",
        "Very tasty! Would make again with minor tweaks.",
        "Good recipe overall, easy to follow.",
        "Nice dish! Added some extra spices to taste.",
        "Solid recipe, turned out well.",
        "Pretty good! Family enjoyed it.",
        "Good results, though prep time was longer.",
        "Tasty! Added more garlic and it was perfect.",
        "Great recipe! Cooking time was a bit off but delicious.",
        "Very good! Would reduce salt next time.",
        "Nice flavors! Easy to make on weeknights.",
        "Good recipe! Kids loved it, adults wanted more seasoning.",
        "Turned out well! Next time I'll add more vegetables.",
        "Solid dish! Would make again with small modifications.",
        "Good results! Instructions were mostly clear.",
        "Tasty meal! Took longer than stated but worth it.",
        "Nice recipe! Added some herbs for extra flavor.",
        "Good dish! Would recommend with minor adjustments.",
        "Pretty tasty! Easy enough for a busy weeknight."
    ],
    3: [
        "Decent recipe, nothing special but okay.",
        "Average results. It was fine but not amazing.",
        "Okay recipe, might try variations next time.",
        "Not bad, but I've had better versions.",
        "Acceptable results, fairly easy to make.",
        "It was alright, nothing to write home about.",
        "Mediocre flavors, but edible.",
        "Fair recipe, could use more seasoning.",
        "Okay dish. Needed more flavor for my taste.",
        "Average recipe. Kids ate it but weren't excited.",
        "It was fine. Nothing special but not bad either.",
        "Decent meal. Would try a different version next time.",
        "Okay results. Instructions could be clearer.",
        "Fair dish. Needed more spices to make it interesting.",
        "Average recipe. Turned out as expected, nothing more.",
        "It was alright. Not bad but not great either.",
        "Decent enough. Would probably try something else next time.",
        "Okay meal. Family ate it but no one raved about it.",
        "Fair recipe. Results were just okay.",
        "Average dish. Nothing wrong with it, just bland."
    ],
    2: [
        "Disappointing. Didn't turn out as expected.",
        "Not great. Instructions were unclear.",
        "Below average. Flavors were bland.",
        "Didn't work well for me. Too complicated.",
        "Poor results despite following instructions.",
        "Not impressed. Waste of ingredients.",
        "Difficult to follow and mediocre taste.",
        "Expected better based on other reviews.",
        "Disappointing results. Too salty for our taste.",
        "Not good. Instructions were confusing.",
        "Below expectations. Texture was all wrong.",
        "Poor dish. Took too long for mediocre results.",
        "Not impressed. Flavors didn't work together.",
        "Disappointing. Much better recipes out there.",
        "Not great. Kids refused to eat it.",
        "Poor results. Instructions need improvement.",
        "Below average. Wouldn't make again.",
        "Disappointing meal. Expected much better.",
        "Not good. Waste of time and ingredients.",
        "Poor recipe. Results were not appetizing."
    ],
    1: [
        "Terrible recipe! Complete disaster.",
        "Awful! Couldn't even finish eating it.",
        "Worst recipe I've ever tried.",
        "Completely inedible. Don't waste your time.",
        "Horrible! Instructions made no sense.",
        "Failed miserably. Very disappointed.",
        "Disgusting results. Never again.",
        "Total failure. Threw it all away.",
        "Absolutely terrible! What a waste of ingredients.",
        "Horrible! Even the dog wouldn't eat it.",
        "Worst meal ever. Instructions were completely wrong.",
        "Terrible! Burned everything following the directions.",
        "Awful recipe. Completely inedible mess.",
        "Horrible results. Don't bother with this one.",
        "Terrible! Wasted my entire evening.",
        "Awful! Instructions are completely off.",
        "Horrible dish. Couldn't even salvage it.",
        "Terrible recipe. Save your money and time.",
        "Awful results. This recipe needs major work.",
        "Horrible! One of the worst I've ever tried."
    ]
}

# Additional review modifiers for more variety
REVIEW_MODIFIERS = {
    'positive': [
        " The whole family loved it!",
        " Will definitely be making this again.",
        " Easy to follow instructions.",
        " Perfect for weeknight dinners.",
        " Great flavors!",
        " Turned out exactly as pictured.",
        " Even my picky eater enjoyed it.",
        " Quick and delicious!",
        " Restaurant quality at home.",
        " Made it for guests and they raved about it."
    ],
    'neutral': [
        " Instructions were mostly clear.",
        " Took about the expected time to make.",
        " Results were as described.",
        " Nothing surprising but solid.",
        " Would work for a regular dinner.",
        " Decent for the effort required.",
        " Kids ate it without complaints.",
        " Good enough for a weeknight meal."
    ],
    'negative': [
        " Instructions could be much clearer.",
        " Took way longer than stated.",
        " Results didn't match the description.",
        " Too much work for the outcome.",
        " Kids refused to eat it.",
        " Wouldn't recommend to others.",
        " Better recipes available elsewhere.",
        " Not worth the time and effort."
    ]
}

# Cooking skill specific comments
SKILL_BASED_COMMENTS = {
    'beginner': [
        "As a beginner cook, I found this easy to follow.",
        "Perfect recipe for someone just starting to cook.",
        "Simple enough for a novice like me.",
        "Great starter recipe!",
        "Easy for beginners but still tasty.",
        "Not too complicated for someone new to cooking."
    ],
    'intermediate': [
        "Good technique practice for intermediate cooks.",
        "Nice recipe with some interesting techniques.",
        "Good balance of challenge and achievability.",
        "Enjoyed the cooking process.",
        "Good recipe for expanding cooking skills.",
        "Nice step up from basic recipes."
    ],
    'advanced': [
        "Technique could be refined but good base recipe.",
        "Would suggest some modifications to the method.",
        "Good foundation but room for improvement.",
        "Interesting approach, though I'd do some things differently.",
        "Solid recipe with potential for enhancement.",
        "Good concept, execution could be better."
    ]
}

class SyntheticDataGenerator:
    def __init__(self):
        self.client = pymongo.MongoClient(MONGO_URI)
        self.db = self.client[DATABASE_NAME]
        self.recipes = []
        self.users = []
        self.generated_data = {
            'users': 0,
            'reviews': 0,
            'searches': 0,
            'saved_recipes': 0,
            'interactions': 0
        }
    
    def load_recipes(self):
        """Load existing recipes from the database or JSON file."""
        print("Loading recipes...")
        
        # Try to load from JSON file first
        try:
            with open('data/clean_recipes.json', 'r', encoding='utf-8') as f:
                self.recipes = json.load(f)
            print(f"Loaded {len(self.recipes)} recipes from JSON file")
        except FileNotFoundError:
            print("JSON file not found, trying database...")
            # Try to load from database
            recipes_cursor = self.db.recipes.find({})
            self.recipes = list(recipes_cursor)
            print(f"Loaded {len(self.recipes)} recipes from database")
        
        if not self.recipes:
            raise Exception("No recipes found! Please ensure recipes are available.")
    
    def generate_users(self, num_users=75):
        """Generate realistic user accounts with diverse personas."""
        print(f"Generating {num_users} synthetic users...")
        
        personas = list(USER_PERSONAS.keys())
        
        for i in range(num_users):
            # Select persona
            persona = random.choice(personas)
            persona_data = USER_PERSONAS[persona]
            
            # Generate basic info
            first_name = random.choice(FIRST_NAMES)
            last_name = random.choice(LAST_NAMES)
            email = f"{first_name.lower()}.{last_name.lower()}{random.randint(1, 999)}@email.com"
            
            # Generate registration date (last 2 years)
            days_ago = random.randint(1, 730)
            created_at = datetime.utcnow() - timedelta(days=days_ago)
            
            # Hash password
            password = bcrypt.hashpw("password123".encode('utf-8'), bcrypt.gensalt())
            
            user = {
                'name': f"{first_name} {last_name}",
                'email': email,
                'password': password,
                'profile_image': None,
                'created_at': created_at,
                'updated_at': created_at,
                'persona': persona,  # For our reference
                'preferences': {
                    'favorite_ingredients': [],
                    'dietary_restrictions': persona_data['dietary_restrictions'].copy()
                },
                'saved_recipes': [],
                'dashboard_data': {
                    'recent_searches': [],
                    'ingredient_history': [],
                    'search_stats': {
                        'total_searches': 0,
                        'most_used_ingredients': {},
                        'last_search_date': None
                    }
                },
                'analytics': {
                    'total_recipe_views': 0,
                    'total_recipe_saves': 0,
                    'total_reviews_given': 0,
                    'cuisine_preferences': {},
                    'cooking_streak': {
                        'current_streak': 0,
                        'longest_streak': 0,
                        'last_activity_date': None
                    },
                    'monthly_activity': {},
                    'discovery_stats': {
                        'unique_ingredients_tried': 0,
                        'recipe_diversity_score': 0
                    }
                }
            }
            
            self.users.append(user)
        
        # Insert users into database
        result = self.db.users.insert_many(self.users)
        self.generated_data['users'] = len(result.inserted_ids)
        
        # Update users with their MongoDB IDs
        for i, user_id in enumerate(result.inserted_ids):
            self.users[i]['_id'] = user_id
        
        print(f"✅ Generated {len(self.users)} users")

    def generate_reviews(self):
        """Generate realistic recipe reviews and ratings."""
        print("Generating recipe reviews...")

        reviews_to_insert = []

        # Determine how many recipes should have reviews (increase to 85%)
        recipes_with_reviews = random.sample(self.recipes, min(len(self.recipes), int(len(self.recipes) * 0.85)))

        for recipe in recipes_with_reviews:
            recipe_id = recipe.get('id', str(recipe.get('_id', '')))

            # Determine number of reviews for this recipe (more reviews overall)
            review_count = int(np.random.choice([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
                                              p=[0.2, 0.25, 0.2, 0.12, 0.08, 0.06, 0.04, 0.03, 0.015, 0.005]))

            # Select random users to review this recipe
            reviewers = random.sample(self.users, min(len(self.users), review_count))

            for user in reviewers:
                user_id = str(user['_id'])
                persona = user['persona']
                persona_data = USER_PERSONAS[persona]

                # Generate rating based on persona and recipe
                if persona_data['cooking_skill'] == 'advanced':
                    # Advanced cooks are more critical
                    rating = int(np.random.choice([1, 2, 3, 4, 5], p=[0.05, 0.1, 0.2, 0.35, 0.3]))
                elif persona_data['cooking_skill'] == 'beginner':
                    # Beginners are more forgiving
                    rating = int(np.random.choice([1, 2, 3, 4, 5], p=[0.02, 0.08, 0.15, 0.35, 0.4]))
                else:
                    # Intermediate cooks - balanced
                    rating = int(np.random.choice([1, 2, 3, 4, 5], p=[0.03, 0.07, 0.2, 0.4, 0.3]))

                # Generate enhanced review text
                review_text = random.choice(REVIEW_TEMPLATES[rating])

                # Add skill-based comments (30% chance)
                if random.random() < 0.3:
                    skill_comment = random.choice(SKILL_BASED_COMMENTS[persona_data['cooking_skill']])
                    review_text += " " + skill_comment

                # Add modifiers based on rating (40% chance)
                if random.random() < 0.4:
                    if rating >= 4:
                        modifier = random.choice(REVIEW_MODIFIERS['positive'])
                    elif rating == 3:
                        modifier = random.choice(REVIEW_MODIFIERS['neutral'])
                    else:
                        modifier = random.choice(REVIEW_MODIFIERS['negative'])
                    review_text += modifier

                # Add specific cooking details (20% chance for ratings 3+)
                if rating >= 3 and random.random() < 0.2:
                    cooking_details = [
                        " Cooking time was spot on.",
                        " Had to adjust seasoning to taste.",
                        " Used what I had in the pantry.",
                        " Made some substitutions and it worked well.",
                        " Followed the recipe exactly.",
                        " Added extra vegetables for nutrition.",
                        " Perfect portion size for our family.",
                        " Great use of leftover ingredients."
                    ]
                    review_text += random.choice(cooking_details)

                # Generate review date (after user registration, before now)
                days_since_registration = (datetime.utcnow() - user['created_at']).days
                if days_since_registration > 0:
                    review_days_ago = random.randint(1, min(days_since_registration, 365))
                    review_date = datetime.utcnow() - timedelta(days=review_days_ago)
                else:
                    review_date = datetime.utcnow()

                review = {
                    'recipe_id': recipe_id,
                    'user_id': user_id,
                    'user_name': user['name'],
                    'rating': rating,
                    'review_text': review_text,
                    'helpful_votes': random.randint(0, max(1, review_count * 2)),
                    'unhelpful_votes': random.randint(0, max(1, review_count // 2)),
                    'created_at': review_date,
                    'updated_at': review_date
                }

                reviews_to_insert.append(review)

        # Insert reviews into database
        if reviews_to_insert:
            result = self.db.recipe_reviews.insert_many(reviews_to_insert)
            self.generated_data['reviews'] = len(result.inserted_ids)

        print(f"✅ Generated {len(reviews_to_insert)} reviews")

    def generate_search_history(self):
        """Generate realistic search patterns for users."""
        print("Generating search history...")

        searches_generated = 0

        for user in self.users:
            user_id = str(user['_id'])
            persona = user['persona']
            persona_data = USER_PERSONAS[persona]

            # Determine search frequency based on persona
            if persona_data['search_frequency'] == 'high':
                num_searches = random.randint(15, 40)
            elif persona_data['search_frequency'] == 'medium':
                num_searches = random.randint(8, 20)
            else:  # low
                num_searches = random.randint(2, 10)

            recent_searches = []
            ingredient_history = []
            most_used_ingredients = defaultdict(int)

            # Generate searches over time
            days_since_registration = (datetime.utcnow() - user['created_at']).days

            for _ in range(num_searches):
                # Generate search date
                if days_since_registration > 0:
                    search_days_ago = random.randint(1, min(days_since_registration, 180))
                    search_date = datetime.utcnow() - timedelta(days=search_days_ago)
                else:
                    search_date = datetime.utcnow()

                # Select ingredients based on persona preferences
                if random.random() < 0.7:  # 70% use common combinations
                    ingredients = random.choice(INGREDIENT_COMBINATIONS).copy()
                    # Sometimes add extra ingredients
                    if random.random() < 0.3:
                        extra_ingredients = ['garlic', 'onion', 'salt', 'pepper', 'herbs', 'oil']
                        ingredients.append(random.choice(extra_ingredients))
                else:  # 30% use random ingredients from recipes
                    # Pick random recipe and use some of its ingredients
                    random_recipe = random.choice(self.recipes)
                    recipe_ingredients = random_recipe.get('ingredients', [])
                    if recipe_ingredients:
                        num_ingredients = random.randint(1, min(4, len(recipe_ingredients)))
                        ingredients = random.sample(recipe_ingredients, num_ingredients)
                    else:
                        ingredients = random.choice(INGREDIENT_COMBINATIONS).copy()

                # Clean and format ingredients
                ingredients = [ing.lower().strip() for ing in ingredients]
                ingredients_str = ', '.join(ingredients)

                # Create search entry
                search_entry = {
                    'ingredients': ingredients_str,
                    'timestamp': search_date,
                    'results_count': random.randint(5, 25)
                }

                recent_searches.append(search_entry)

                # Update ingredient history and stats
                for ingredient in ingredients:
                    if ingredient not in ingredient_history:
                        ingredient_history.append(ingredient)
                    most_used_ingredients[ingredient] += 1

            # Sort searches by date (most recent first)
            recent_searches.sort(key=lambda x: x['timestamp'], reverse=True)
            recent_searches = recent_searches[:15]  # Keep only 15 most recent

            # Keep only 15 most recent ingredients
            ingredient_history = ingredient_history[-15:]

            # Update user's dashboard data
            dashboard_update = {
                'recent_searches': recent_searches,
                'ingredient_history': ingredient_history,
                'search_stats': {
                    'total_searches': num_searches,
                    'most_used_ingredients': dict(most_used_ingredients),
                    'last_search_date': recent_searches[0]['timestamp'] if recent_searches else None
                }
            }

            # Update user in database
            self.db.users.update_one(
                {'_id': user['_id']},
                {
                    '$set': {
                        'dashboard_data': dashboard_update,
                        'updated_at': datetime.utcnow()
                    }
                }
            )

            searches_generated += num_searches

        self.generated_data['searches'] = searches_generated
        print(f"✅ Generated {searches_generated} search entries")

    def generate_saved_recipes(self):
        """Generate realistic saved recipe patterns."""
        print("Generating saved recipes...")

        total_saves = 0

        for user in self.users:
            user_id = str(user['_id'])
            persona = user['persona']
            persona_data = USER_PERSONAS[persona]

            # Determine number of saved recipes based on persona
            if persona_data['recipe_saves'] == 'many':
                num_saves = random.randint(10, 30)
            elif persona_data['recipe_saves'] == 'medium':
                num_saves = random.randint(5, 15)
            else:  # few
                num_saves = random.randint(1, 8)

            # Select recipes to save (prefer recipes matching user's cuisine preferences)
            preferred_cuisines = persona_data['preferred_cuisines']

            # Get recipes matching preferences (70% of saves)
            preferred_recipes = [r for r in self.recipes
                               if r.get('cuisine', 'International') in preferred_cuisines]

            saved_recipe_ids = []

            # Save preferred recipes
            if preferred_recipes:
                preferred_saves = min(int(num_saves * 0.7), len(preferred_recipes))
                saved_recipes = random.sample(preferred_recipes, preferred_saves)

                for recipe in saved_recipes:
                    # Save recipe to database first
                    recipe_data = {
                        'original_id': recipe.get('id', str(recipe.get('_id', ''))),
                        'name': recipe['name'],
                        'ingredients': recipe['ingredients'],
                        'steps': recipe.get('instructions', []),
                        'techniques': [],
                        'calorie_level': 1,
                        'prep_time': recipe.get('prep_time', 30),
                        'cook_time': recipe.get('cook_time', 45),
                        'servings': recipe.get('servings', 4),
                        'cuisine': recipe.get('cuisine', 'International'),
                        'difficulty': recipe.get('difficulty', 'Medium'),
                        'created_at': datetime.utcnow(),
                        'updated_at': datetime.utcnow()
                    }

                    # Check if recipe already exists in database
                    existing_recipe = self.db.recipes.find_one({'original_id': recipe_data['original_id']})
                    if existing_recipe:
                        recipe_id = str(existing_recipe['_id'])
                    else:
                        result = self.db.recipes.insert_one(recipe_data)
                        recipe_id = str(result.inserted_id)

                    saved_recipe_ids.append(recipe_id)

            # Save some random recipes (30% of saves)
            remaining_saves = num_saves - len(saved_recipe_ids)
            if remaining_saves > 0:
                available_recipes = [r for r in self.recipes if r not in saved_recipes]
                if available_recipes:
                    random_saves = min(remaining_saves, len(available_recipes))
                    random_recipes = random.sample(available_recipes, random_saves)

                    for recipe in random_recipes:
                        recipe_data = {
                            'original_id': recipe.get('id', str(recipe.get('_id', ''))),
                            'name': recipe['name'],
                            'ingredients': recipe['ingredients'],
                            'steps': recipe.get('instructions', []),
                            'techniques': [],
                            'calorie_level': 1,
                            'prep_time': recipe.get('prep_time', 30),
                            'cook_time': recipe.get('cook_time', 45),
                            'servings': recipe.get('servings', 4),
                            'cuisine': recipe.get('cuisine', 'International'),
                            'difficulty': recipe.get('difficulty', 'Medium'),
                            'created_at': datetime.utcnow(),
                            'updated_at': datetime.utcnow()
                        }

                        existing_recipe = self.db.recipes.find_one({'original_id': recipe_data['original_id']})
                        if existing_recipe:
                            recipe_id = str(existing_recipe['_id'])
                        else:
                            result = self.db.recipes.insert_one(recipe_data)
                            recipe_id = str(result.inserted_id)

                        saved_recipe_ids.append(recipe_id)

            # Update user's saved recipes
            self.db.users.update_one(
                {'_id': user['_id']},
                {
                    '$set': {
                        'saved_recipes': saved_recipe_ids,
                        'analytics.total_recipe_saves': len(saved_recipe_ids),
                        'updated_at': datetime.utcnow()
                    }
                }
            )

            total_saves += len(saved_recipe_ids)

        self.generated_data['saved_recipes'] = total_saves
        print(f"✅ Generated {total_saves} saved recipes")

    def generate_community_interactions(self):
        """Generate helpful/unhelpful votes on reviews."""
        print("Generating community interactions...")

        # Get all reviews
        reviews = list(self.db.recipe_reviews.find({}))
        interactions_generated = 0

        for review in reviews:
            review_id = str(review['_id'])
            review_user_id = review['user_id']

            # Determine number of votes (some reviews get no votes)
            if random.random() < 0.6:  # 60% of reviews get votes
                num_votes = random.randint(1, 8)

                # Select random users to vote (excluding the review author)
                potential_voters = [u for u in self.users if str(u['_id']) != review_user_id]
                voters = random.sample(potential_voters, min(len(potential_voters), num_votes))

                helpful_votes = 0
                unhelpful_votes = 0

                for voter in voters:
                    voter_id = str(voter['_id'])

                    # Determine vote type (helpful votes are more common for good reviews)
                    if review['rating'] >= 4:
                        is_helpful = random.random() < 0.8  # 80% helpful for good reviews
                    elif review['rating'] == 3:
                        is_helpful = random.random() < 0.5  # 50% helpful for neutral reviews
                    else:
                        is_helpful = random.random() < 0.3  # 30% helpful for bad reviews

                    # Create vote record
                    vote_data = {
                        'review_id': review_id,
                        'user_id': voter_id,
                        'is_helpful': is_helpful,
                        'created_at': review['created_at'] + timedelta(days=random.randint(1, 30))
                    }

                    # Insert vote (with duplicate handling)
                    try:
                        self.db.review_votes.insert_one(vote_data)
                        if is_helpful:
                            helpful_votes += 1
                        else:
                            unhelpful_votes += 1
                        interactions_generated += 1
                    except pymongo.errors.DuplicateKeyError:
                        # Skip if duplicate vote
                        pass

                # Update review with vote counts
                self.db.recipe_reviews.update_one(
                    {'_id': review['_id']},
                    {
                        '$set': {
                            'helpful_votes': helpful_votes,
                            'unhelpful_votes': unhelpful_votes
                        }
                    }
                )

        self.generated_data['interactions'] = interactions_generated
        print(f"✅ Generated {interactions_generated} community interactions")

    def update_user_analytics(self):
        """Update user analytics based on generated data."""
        print("Updating user analytics...")

        for user in self.users:
            user_id = str(user['_id'])

            # Count user's reviews
            review_count = self.db.recipe_reviews.count_documents({'user_id': user_id})

            # Get user's saved recipes count
            saved_count = len(user.get('saved_recipes', []))

            # Calculate cuisine preferences based on saved recipes
            cuisine_preferences = defaultdict(int)
            for recipe_id in user.get('saved_recipes', []):
                try:
                    recipe = self.db.recipes.find_one({'_id': ObjectId(recipe_id)})
                    if recipe:
                        cuisine = recipe.get('cuisine', 'International')
                        cuisine_preferences[cuisine] += 1
                except:
                    pass

            # Generate some activity patterns
            search_stats = user.get('dashboard_data', {}).get('search_stats', {})
            total_searches = search_stats.get('total_searches', 0)

            # Calculate cooking streak (random but realistic)
            current_streak = random.randint(0, 15)
            longest_streak = max(current_streak, random.randint(current_streak, 30))

            # Generate monthly activity (last 6 months)
            monthly_activity = {}
            for i in range(6):
                month_date = datetime.utcnow() - timedelta(days=30 * i)
                month_key = month_date.strftime('%Y-%m')
                monthly_activity[month_key] = {
                    'searches': random.randint(0, max(1, total_searches // 6)),
                    'recipe_views': random.randint(0, max(1, total_searches * 2)),
                    'recipes_saved': random.randint(0, max(1, saved_count // 6))
                }

            # Update analytics
            analytics_update = {
                'total_recipe_views': total_searches * random.randint(2, 5),
                'total_recipe_saves': saved_count,
                'total_reviews_given': review_count,
                'cuisine_preferences': dict(cuisine_preferences),
                'cooking_streak': {
                    'current_streak': current_streak,
                    'longest_streak': longest_streak,
                    'last_activity_date': datetime.utcnow() - timedelta(days=random.randint(1, 7))
                },
                'monthly_activity': monthly_activity,
                'discovery_stats': {
                    'unique_ingredients_tried': len(user.get('dashboard_data', {}).get('ingredient_history', [])),
                    'recipe_diversity_score': min(100, len(cuisine_preferences) * 20)
                }
            }

            # Update user analytics
            self.db.users.update_one(
                {'_id': user['_id']},
                {
                    '$set': {
                        'analytics': analytics_update,
                        'updated_at': datetime.utcnow()
                    }
                }
            )

        print("✅ Updated user analytics")

    def generate_all_data(self, num_users=75):
        """Generate all synthetic data."""
        print("🚀 Starting synthetic data generation for SisaRasa")
        print("=" * 60)

        try:
            # Load recipes first
            self.load_recipes()

            # Generate data in sequence
            self.generate_users(num_users)
            self.generate_reviews()
            self.generate_search_history()
            self.generate_saved_recipes()
            self.generate_community_interactions()
            self.update_user_analytics()

            # Print summary
            print("\n" + "=" * 60)
            print("🎉 SYNTHETIC DATA GENERATION COMPLETE!")
            print("=" * 60)
            print(f"👥 Users created: {self.generated_data['users']}")
            print(f"⭐ Reviews generated: {self.generated_data['reviews']}")
            print(f"🔍 Search entries: {self.generated_data['searches']}")
            print(f"💾 Saved recipes: {self.generated_data['saved_recipes']}")
            print(f"👍 Community interactions: {self.generated_data['interactions']}")
            print("\n📊 Your SisaRasa system now has realistic data to showcase:")
            print("   • KNN ingredient-based recommendations")
            print("   • Collaborative filtering from user ratings")
            print("   • Content-based filtering from user preferences")
            print("   • Popularity-based recommendations")
            print("   • User analytics and engagement metrics")
            print("\n🎯 Ready to test your hybrid recommendation system!")

        except Exception as e:
            print(f"❌ Error during data generation: {e}")
            raise

        finally:
            self.client.close()

def main():
    """Main function to run synthetic data generation."""
    print("SisaRasa Synthetic Data Generator")
    print("This will populate your MongoDB database with realistic user data.")

    # Ask for confirmation
    response = input("\n⚠️  This will add synthetic data to your database. Continue? (y/N): ")
    if response.lower() != 'y':
        print("Operation cancelled.")
        return

    # Ask for number of users
    try:
        num_users = input("\nHow many users to generate? (default: 75): ").strip()
        num_users = int(num_users) if num_users else 75
        if num_users < 1 or num_users > 200:
            print("Number of users should be between 1 and 200. Using default: 75")
            num_users = 75
    except ValueError:
        print("Invalid input. Using default: 75 users")
        num_users = 75

    # Generate data
    generator = SyntheticDataGenerator()
    generator.generate_all_data(num_users)

if __name__ == "__main__":
    main()
